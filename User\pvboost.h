/*
 * pvboost.h
 *
 *  Created on: 2024年5月23日
 *      Author: ZH
 */

#ifndef INCLUDE_PVBOOST_H_
#define INCLUDE_PVBOOST_H_

#include "common.h"

typedef struct
{
    float vbus_base;
    uint32_t pwm_base;

    float Vinv_avg;
    float Vgrid_avg;

    float amp_err;
    float amp_err_min;
    uint32_t amp_err_state; // 记录最近32次差值状态

    float power_target;
    float power_ctlr;
    int32_t MaxCap;
    uint32_t is_enable;
} PVBoost_Handle_t;

void PVBoost_Init(PVBoost_Handle_t *PVB);

#endif /* INCLUDE_PVBOOST_H_ */
